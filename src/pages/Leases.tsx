import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { LeasesDataTable } from '@/components/leases/LeasesDataTable';
import { LeasesFilters } from '@/components/leases/LeasesFilters';
import { Button } from '@/components/ui/button';
import { Plus, Filter, RefreshCw } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

const Leases = () => {
  const navigate = useNavigate();
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({});

  const { data: leases, isLoading, error, refetch } = useQuery({
    queryKey: ['leases', searchTerm, statusFilter, dateRange],
    queryFn: async () => {
      let query = supabase
        .from('lease_documents')
        .select(`
          *,
          parties(party_name, party_type, address_street, address_city),
          properties(building_address, leased_premises_address, rental_area_sqft),
          financial_terms(monthly_base_rent, annual_base_rent, rent_currency)
        `);

      // Apply search filter if provided
      if (searchTerm) {
        // For search, we need to handle the case where parties might be null
        query = query.or(`
          properties.building_address.ilike.%${searchTerm}%,
          properties.leased_premises_address.ilike.%${searchTerm}%,
          parties.party_name.ilike.%${searchTerm}%
        `);
      }

      // Apply status filter
      if (statusFilter.length > 0) {
        query = query.in('lease_status', statusFilter);
      }

      // Apply date range filter
      if (dateRange.from) {
        query = query.gte('commencement_date', dateRange.from.toISOString());
      }
      if (dateRange.to) {
        query = query.lte('end_date', dateRange.to.toISOString());
      }

      const { data, error } = await query.order('created_date', { ascending: false });
      
      if (error) throw error;
      
      // Process the data to ensure proper structure
      const processedData = data?.map(lease => ({
        ...lease,
        parties: lease.parties || [],
        properties: lease.properties || [],
        financial_terms: lease.financial_terms || []
      }));
      
      return processedData;
    },
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <p className="text-red-600">Error loading leases: {error.message}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Lease Management</h1>
          <p className="text-gray-600 mt-1">
            Manage all your lease agreements and tenant relationships
          </p>
        </div>
        <div className="flex gap-3">
          <Button 
            variant="outline" 
            onClick={() => refetch()}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
          <Button 
            variant="outline" 
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filters
          </Button>
          <Button 
            onClick={() => navigate('/leases/new')}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            New Lease
          </Button>
        </div>
      </div>

      {showFilters && (
        <div className="w-full">
          <LeasesFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            statusFilter={statusFilter}
            onStatusFilterChange={setStatusFilter}
            dateRange={dateRange}
            onDateRangeChange={setDateRange}
          />
        </div>
      )}

      <div className="w-full">
        <LeasesDataTable leases={leases || []} />
      </div>
    </div>
  );
};

export default Leases;
