
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { LeaseDetailHeader } from '@/components/leases/LeaseDetailHeader';
import { LeaseDetailTabs } from '@/components/leases/LeaseDetailTabs';
import { normalizeStatus } from '@/utils/statusUtils';

const LeaseDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const { data: lease, isLoading, error } = useQuery({
    queryKey: ['lease', id],
    queryFn: async () => {
      if (!id) throw new Error('Lease ID is required');

      // Fetch main lease data first
      const { data: leaseData, error: leaseError } = await supabase
        .from('lease_documents')
        .select('*')
        .eq('lease_id', id)
        .single();

      if (leaseError) throw leaseError;

      // Fetch all related data in parallel
      const [
        parties,
        properties,
        financialTerms,
        securityDeposits,
        useRestrictions,
        maintenanceObligations,
        complianceRequirements,
        documentAttachments,
        signatures,
        assignmentTerms,
        accessRights,
        defaultRemedies,
        insuranceTerms,
        legalProvisions,
        noticeProvisions,
        chatSessions,
        messages,
        currentView,
        rentEscalations,
        operatingCostsDetails,
        guarantors,
        renewalOptions,
        tenantInducements,
        earlyTerminationOptions,
        brokers,
        improvementTerms,
      ] = await Promise.all([
        supabase.from('parties').select('*').eq('lease_id', id),
        supabase.from('properties').select('*').eq('lease_id', id),
        supabase.from('financial_terms').select('*').eq('lease_id', id),
        supabase.from('security_deposits').select('*').eq('lease_id', id),
        supabase.from('use_restrictions').select('*').eq('lease_id', id),
        supabase.from('maintenance_obligations').select('*').eq('lease_id', id),
        supabase.from('compliance_requirements').select('*').eq('lease_id', id),
        supabase.from('document_attachments').select('*').eq('lease_id', id),
        supabase.from('signatures').select('*').eq('lease_id', id),
        supabase.from('assignment_subletting_terms').select('*').eq('lease_id', id),
        supabase.from('access_inspection_rights').select('*').eq('lease_id', id),
        supabase.from('default_remedies').select('*').eq('lease_id', id),
        supabase.from('insurance_liability_terms').select('*').eq('lease_id', id),
        supabase.from('legal_general_provisions').select('*').eq('lease_id', id),
        supabase.from('notice_provisions').select('*').eq('lease_id', id),
        supabase.from('chat_sessions').select('*').eq('lease_id', id),
        supabase.from('messages').select('*').eq('lease_id', id),
        supabase.from('current_view').select('*').eq('lease_id', id),
        (supabase as any).from('rent_escalations').select('*').eq('lease_id', id),
        (supabase as any).from('operating_costs_details').select('*').eq('lease_id', id),
        (supabase as any).from('guarantors').select('*').eq('lease_id', id),
        (supabase as any).from('renewal_options').select('*').eq('lease_id', id),
        (supabase as any).from('tenant_inducements').select('*').eq('lease_id', id),
        (supabase as any).from('early_termination_options').select('*').eq('lease_id', id),
        (supabase as any).from('brokers').select('*').eq('lease_id', id),
        (supabase as any).from('improvement_terms').select('*').eq('lease_id', id),
      ]);

      // Combine all data
      const data = {
        ...leaseData,
        parties: parties.data || [],
        properties: properties.data || [],
        financial_terms: financialTerms.data || [],
        security_deposits: securityDeposits.data || [],
        use_restrictions: useRestrictions.data || [],
        maintenance_obligations: maintenanceObligations.data || [],
        compliance_requirements: complianceRequirements.data || [],
        document_attachments: documentAttachments.data || [],
        signatures: signatures.data || [],
        assignment_subletting_terms: assignmentTerms.data || [],
        access_inspection_rights: accessRights.data || [],
        default_remedies: defaultRemedies.data || [],
        insurance_liability_terms: insuranceTerms.data || [],
        legal_general_provisions: legalProvisions.data || [],
        notice_provisions: noticeProvisions.data || [],
        chat_sessions: chatSessions.data || [],
        messages: messages.data || [],
        current_view: currentView.data || [],
        rent_escalations: rentEscalations.data || [],
        operating_costs_details: operatingCostsDetails.data || [],
        guarantors: guarantors.data || [],
        renewal_options: renewalOptions.data || [],
        tenant_inducements: tenantInducements.data || [],
        early_termination_options: earlyTerminationOptions.data || [],
        brokers: brokers.data || [],
        improvement_terms: improvementTerms.data || [],
        // Placeholder for other new tables
        environmental_provisions: [],
        purchase_options: [],
        premises_condition_terms: [],
        dispute_resolution: [],
        leased_amenities: [],
      };

      return data;
    },
    enabled: !!id,
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !lease) {
    return (
      <div className="text-center p-8">
        <p className="text-red-600">Error loading lease details</p>
        <Button onClick={() => navigate('/leases')} className="mt-4">
          Back to Leases
        </Button>
      </div>
    );
  }

  const tenantName = (lease as any).parties?.find((p: any) => p.party_type === 'tenant')?.party_name || 'Unknown Tenant';
  const property = (lease as any).properties?.[0];
  const propertyAddress = property?.building_address || property?.leased_premises_address || 'Unknown Property';
  const financialTerms = (lease as any).financial_terms?.[0];
  const monthlyRent = financialTerms?.monthly_base_rent;
  const currency = financialTerms?.rent_currency || 'CAD';
  const securityDeposit = (lease as any).security_deposits?.[0];

  console.log('Lease object passed to LeaseDetailTabs/LeaseCurrentView (after type update):', lease);

  return (
    <div className="space-y-6">
      <LeaseDetailHeader
        lease={lease}
        tenantName={tenantName}
        propertyAddress={propertyAddress}
        leaseStatus={(lease as any).lease_status}
        monthlyRent={monthlyRent}
        currency={currency}
        rentalAreaSqft={property?.rental_area_sqft}
        securityDepositTotal={securityDeposit?.security_deposit_total}
        leaseTermMonths={(lease as any).lease_term_months}
        normalizeStatus={normalizeStatus}
      />

      <LeaseDetailTabs lease={lease} />
    </div>
  );
};

export default LeaseDetail;
