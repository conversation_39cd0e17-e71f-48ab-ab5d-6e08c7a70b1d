import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Building, MapPin, Ruler, Wrench, Home, Users } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface Property {
  property_id: string;
  leased_premises_address: string;
  building_address: string;
  land_lot_number: string;
  cadastre_details: string;
  rental_area_sqft: number;
  measurement_method: string;
  property_condition: string;
  plan_reference: string;
  physical_boundaries_description: string;
}

interface PremiseCondition {
  id: string;
  condition_type: string;
  description: string;
  landlord_obligations: string;
  tenant_obligations: string;
  make_good_requirements: string;
  delivery_condition_description: string;
}

interface UseRestriction {
  restriction_id: string;
  restriction_type: string;
  restriction_description: string;
  restriction_category: string;
}

interface ImprovementTerm {
  improvement_id: string;
  improvement_type: string;
  cost_responsibility: string;
  admin_fee_percentage: number;
  quality_requirements: string;
  approval_required: boolean;
  contractor_restrictions: string;
  ownership_terms: string;
  construction_timeline_days: number;
  permit_requirements: string;
  lien_waiver_requirements: string;
  restriction_description: string;
  removal_requirements_details: string;
}

interface Amenity {
  id: string;
  amenity_type: string;
  quantity: number;
  location_description: string;
  cost_per_unit: number;
  exclusivity: boolean;
  cost_per_unit_frequency: string;
  cost_escalation_terms: string;
  termination_terms: string;
}

interface LeasePropertyRightsProps {
  lease: any;
  properties?: Property[];
  premiseConditions?: PremiseCondition[];
  useRestrictions?: UseRestriction[];
  improvementTerms?: ImprovementTerm[];
  amenities?: Amenity[];
}

export const LeasePropertyRights = ({ 
  lease, 
  properties = [], 
  premiseConditions = [], 
  useRestrictions = [], 
  improvementTerms = [], 
  amenities = [] 
}: LeasePropertyRightsProps) => {
  
  const totalItems = properties.length + premiseConditions.length + useRestrictions.length + improvementTerms.length + amenities.length;
  const mainProperty = properties[0];

  return (
    <div className="space-y-6">
      {/* Main Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Property & Usage Rights</h2>
          <p className="text-gray-600 mt-1">Property details and usage parameters</p>
        </div>
        <Badge variant="outline" className="text-sm">
          {totalItems} Property Components
        </Badge>
      </div>

      {/* Property Overview */}
      {mainProperty && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 rounded-lg bg-blue-50">
                  <Building className="h-5 w-5 text-blue-600" />
                </div>
                Property Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">{mainProperty.leased_premises_address}</p>
                    {mainProperty.building_address && mainProperty.building_address !== mainProperty.leased_premises_address && (
                      <p className="text-sm text-gray-600">Building: {mainProperty.building_address}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Ruler className="h-4 w-4 text-gray-500" />
                  <div>
                    <span className="font-medium">{mainProperty.rental_area_sqft?.toLocaleString() || 'N/A'} sq ft</span>
                    {mainProperty.measurement_method && (
                      <span className="text-sm text-gray-600 ml-2">({mainProperty.measurement_method})</span>
                    )}
                  </div>
                </div>

                {mainProperty.land_lot_number && (
                  <div className="text-sm">
                    <span className="text-gray-600">Lot: </span>
                    <span className="font-medium">{mainProperty.land_lot_number}</span>
                  </div>
                )}

                {mainProperty.cadastre_details && (
                  <div className="text-sm">
                    <span className="text-gray-600">Cadastre: </span>
                    <span className="font-medium">{mainProperty.cadastre_details}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 rounded-lg bg-green-50">
                  <Home className="h-5 w-5 text-green-600" />
                </div>
                Property Condition
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                {mainProperty.property_condition && (
                  <div>
                    <p className="text-sm text-gray-600">Current Condition</p>
                    <p className="font-medium">{mainProperty.property_condition}</p>
                  </div>
                )}

                {mainProperty.plan_reference && (
                  <div>
                    <p className="text-sm text-gray-600">Plan Reference</p>
                    <p className="font-medium">{mainProperty.plan_reference}</p>
                  </div>
                )}

                {mainProperty.physical_boundaries_description && (
                  <div>
                    <p className="text-sm text-gray-600">Physical Boundaries</p>
                    <p className="text-sm text-gray-700">{mainProperty.physical_boundaries_description}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Assignment & Subletting Terms */}
      {lease.assignment_subletting_terms && lease.assignment_subletting_terms.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 rounded-lg bg-orange-50">
                <Users className="h-5 w-5 text-orange-600" />
              </div>
              Assignment & Subletting
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {lease.assignment_subletting_terms.map((term: any) => (
              <div key={term.assignment_id} className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="font-semibold text-orange-900">{term.transfer_type}</h4>
                      <p className="text-orange-700 text-sm">
                        {term.consent_required ? 'Consent Required' : 'No Consent Required'}
                      </p>
                    </div>
                    <div className="text-right text-sm">
                      {term.response_time_days && (
                        <p className="text-orange-600">{term.response_time_days} days response</p>
                      )}
                      {term.admin_fee && (
                        <p className="text-orange-600">${term.admin_fee.toLocaleString()} admin fee</p>
                      )}
                    </div>
                  </div>
                  
                  {term.refusal_reasons && (
                    <div className="p-3 bg-white rounded border border-orange-200">
                      <p className="text-sm font-medium text-gray-700 mb-1">Refusal Reasons:</p>
                      <p className="text-sm text-gray-600">{term.refusal_reasons}</p>
                    </div>
                  )}
                  
                  {term.information_requirements && (
                    <div className="p-3 bg-white rounded border border-orange-200">
                      <p className="text-sm font-medium text-gray-700 mb-1">Information Requirements:</p>
                      <p className="text-sm text-gray-600">{term.information_requirements}</p>
                    </div>
                  )}
                </div>
                
                {/* Enhanced Assignment Terms */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {(term as any).landlord_consent_factors && (
                    <div className="p-3 bg-white rounded border border-orange-200">
                      <p className="text-sm font-medium text-gray-700 mb-1">Landlord Consent Factors:</p>
                      <p className="text-sm text-gray-600">{(term as any).landlord_consent_factors}</p>
                    </div>
                  )}
                  {(term as any).recapture_excess_consideration_details && (
                    <div className="p-3 bg-white rounded border border-orange-200">
                      <p className="text-sm font-medium text-gray-700 mb-1">Recapture Excess Consideration:</p>
                      <p className="text-sm text-gray-600">{(term as any).recapture_excess_consideration_details}</p>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {(term as any).landlord_recapture_response_time_days && (
                    <div className="p-3 bg-white rounded border border-orange-200 text-center">
                      <p className="text-sm font-medium text-gray-700">Recapture Response</p>
                      <p className="text-sm text-orange-600">{(term as any).landlord_recapture_response_time_days} days</p>
                    </div>
                  )}
                  {(term as any).tenant_withdrawal_period_days && (
                    <div className="p-3 bg-white rounded border border-orange-200 text-center">
                      <p className="text-sm font-medium text-gray-700">Withdrawal Period</p>
                      <p className="text-sm text-orange-600">{(term as any).tenant_withdrawal_period_days} days</p>
                    </div>
                  )}
                  {(term as any).solidary_liability_clause && (
                    <div className="p-3 bg-white rounded border border-orange-200 text-center">
                      <p className="text-sm font-medium text-gray-700">Solidary Liability</p>
                      <p className="text-sm text-orange-600">Required</p>
                    </div>
                  )}
                </div>

                {(term as any).permitted_transfer_conditions && (
                  <div className="p-3 bg-white rounded border border-orange-200">
                    <p className="text-sm font-medium text-gray-700 mb-1">Permitted Transfer Conditions:</p>
                    <p className="text-sm text-gray-600">{(term as any).permitted_transfer_conditions}</p>
                  </div>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Property Rights and Restrictions */}
      <Accordion type="multiple" className="space-y-4">
        {/* Premise Conditions */}
        {premiseConditions.length > 0 && (
          <AccordionItem value="conditions" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-blue-50">
                    <Building className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Premise Conditions</h3>
                    <p className="text-sm text-gray-600">
                      {premiseConditions.length} condition{premiseConditions.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {premiseConditions.map((condition) => (
                    <div key={condition.id} className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-blue-900">{condition.condition_type}</h4>
                            <p className="text-blue-700">{condition.description}</p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {condition.landlord_obligations && (
                            <div className="p-4 bg-white rounded border border-blue-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Landlord Obligations:</p>
                              <p className="text-sm text-gray-600">{condition.landlord_obligations}</p>
                            </div>
                          )}
                          {condition.tenant_obligations && (
                            <div className="p-4 bg-white rounded border border-blue-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Tenant Obligations:</p>
                              <p className="text-sm text-gray-600">{condition.tenant_obligations}</p>
                            </div>
                          )}
                        </div>

                        {condition.make_good_requirements && (
                          <div className="p-4 bg-white rounded border border-blue-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Make Good Requirements:</p>
                            <p className="text-sm text-gray-600">{condition.make_good_requirements}</p>
                          </div>
                        )}

                        {condition.delivery_condition_description && (
                          <div className="p-4 bg-white rounded border border-blue-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Delivery Condition:</p>
                            <p className="text-sm text-gray-600">{condition.delivery_condition_description}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Use Restrictions */}
        {useRestrictions.length > 0 && (
          <AccordionItem value="restrictions" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-red-50">
                    <Users className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Use Restrictions</h3>
                    <p className="text-sm text-gray-600">
                      {useRestrictions.length} restriction{useRestrictions.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {useRestrictions.map((restriction) => (
                    <div key={restriction.restriction_id} className="p-6 bg-red-50 border border-red-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-red-900">{restriction.restriction_type}</h4>
                            <Badge variant="outline" className="mt-1">
                              {restriction.restriction_category}
                            </Badge>
                          </div>
                        </div>
                        
                        {restriction.restriction_description && (
                          <div className="p-4 bg-white rounded border border-red-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Description:</p>
                            <p className="text-sm text-gray-600">{restriction.restriction_description}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Improvement Terms */}
        {improvementTerms.length > 0 && (
          <AccordionItem value="improvements" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-green-50">
                    <Wrench className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Improvement Terms</h3>
                    <p className="text-sm text-gray-600">
                      {improvementTerms.length} improvement type{improvementTerms.length === 1 ? '' : 's'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {improvementTerms.map((term) => (
                    <div key={term.improvement_id} className="p-6 bg-green-50 border border-green-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-green-900">{term.improvement_type}</h4>
                            <p className="text-green-700">Cost responsibility: {term.cost_responsibility}</p>
                          </div>
                          <div className="text-right">
                            {term.approval_required && (
                              <Badge variant="outline">Approval Required</Badge>
                            )}
                            {term.admin_fee_percentage && (
                              <p className="text-sm text-green-700 mt-1">Admin Fee: {term.admin_fee_percentage}%</p>
                            )}
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {term.quality_requirements && (
                            <div className="p-4 bg-white rounded border border-green-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Quality Requirements:</p>
                              <p className="text-sm text-gray-600">{term.quality_requirements}</p>
                            </div>
                          )}
                          {term.contractor_restrictions && (
                            <div className="p-4 bg-white rounded border border-green-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Contractor Restrictions:</p>
                              <p className="text-sm text-gray-600">{term.contractor_restrictions}</p>
                            </div>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {term.ownership_terms && (
                            <div className="p-4 bg-white rounded border border-green-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Ownership Terms:</p>
                              <p className="text-sm text-gray-600">{term.ownership_terms}</p>
                            </div>
                          )}
                          {term.construction_timeline_days && (
                            <div className="p-4 bg-white rounded border border-green-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Timeline:</p>
                              <p className="text-sm text-gray-600">{term.construction_timeline_days} days</p>
                            </div>
                          )}
                        </div>

                        {term.removal_requirements_details && (
                          <div className="p-4 bg-white rounded border border-green-200">
                            <p className="text-sm text-gray-700 font-medium mb-2">Removal Requirements:</p>
                            <p className="text-sm text-gray-600">{term.removal_requirements_details}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}

        {/* Amenities */}
        {amenities.length > 0 && (
          <AccordionItem value="amenities" className="border rounded-lg">
            <Card>
              <AccordionTrigger className="px-6 py-4 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <div className="p-2 rounded-lg bg-purple-50">
                    <Home className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Leased Amenities</h3>
                    <p className="text-sm text-gray-600">
                      {amenities.length} amenity{amenities.length === 1 ? '' : ' types'}
                    </p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <CardContent className="pt-0 space-y-4">
                  {amenities.map((amenity) => (
                    <div key={amenity.id} className="p-6 bg-purple-50 border border-purple-200 rounded-lg">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-semibold text-lg text-purple-900">{amenity.amenity_type}</h4>
                            <p className="text-purple-700">Quantity: {amenity.quantity}</p>
                            <p className="text-purple-700">{amenity.location_description}</p>
                          </div>
                          <div className="text-right">
                            {amenity.exclusivity && (
                              <Badge variant="outline">Exclusive</Badge>
                            )}
                            {amenity.cost_per_unit && (
                              <p className="text-sm text-purple-700 mt-1">
                                ${amenity.cost_per_unit}/{amenity.cost_per_unit_frequency}
                              </p>
                            )}
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {amenity.cost_escalation_terms && (
                            <div className="p-4 bg-white rounded border border-purple-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Cost Escalation:</p>
                              <p className="text-sm text-gray-600">{amenity.cost_escalation_terms}</p>
                            </div>
                          )}
                          {amenity.termination_terms && (
                            <div className="p-4 bg-white rounded border border-purple-200">
                              <p className="text-sm text-gray-700 font-medium mb-2">Termination Terms:</p>
                              <p className="text-sm text-gray-600">{amenity.termination_terms}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </AccordionContent>
            </Card>
          </AccordionItem>
        )}
      </Accordion>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-50">
              <Building className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Conditions</p>
              <p className="text-xl font-semibold">{premiseConditions.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-red-50">
              <Users className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Restrictions</p>
              <p className="text-xl font-semibold">{useRestrictions.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-50">
              <Wrench className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Improvements</p>
              <p className="text-xl font-semibold">{improvementTerms.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-purple-50">
              <Home className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Amenities</p>
              <p className="text-xl font-semibold">{amenities.length}</p>
            </div>
          </div>
        </Card>
      </div>

      {totalItems === 0 && (
        <div className="text-center p-8 text-gray-500">
          No property information available
        </div>
      )}
    </div>
  );
};
