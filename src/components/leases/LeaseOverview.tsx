
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';

interface LeaseOverviewProps {
  lease: any;
}

export const LeaseOverview = ({ lease }: LeaseOverviewProps) => {
  const securityDeposit = lease.security_deposits?.[0];
  const signatures = lease.signatures || [];
  const landlordSignature = signatures.find((s: any) => s.party_type === 'landlord');
  const tenantSignature = signatures.find((s: any) => s.party_type === 'tenant');

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Lease Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Lease Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-600">Lease ID</p>
              <p className="font-medium">{lease.lease_id}</p>
            </div>
            <div>
              <p className="text-gray-600">Status</p>
              <Badge variant="secondary">{lease.lease_status}</Badge>
            </div>
            <div>
              <p className="text-gray-600">Start Date</p>
              <p className="font-medium">
                {lease.commencement_date ? format(new Date(lease.commencement_date), 'PPP') : 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-gray-600">End Date</p>
              <p className="font-medium">
                {lease.end_date ? format(new Date(lease.end_date), 'PPP') : 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-gray-600">Term Duration</p>
              <p className="font-medium">{lease.lease_term_months || 'N/A'} months</p>
            </div>
            <div>
              <p className="text-gray-600">Possession Date</p>
              <p className="font-medium">
                {lease.possession_date ? format(new Date(lease.possession_date), 'PPP') : 'N/A'}
              </p>
            </div>
            {lease.installation_period_months && (
              <div>
                <p className="text-gray-600">Installation Period</p>
                <p className="font-medium">{lease.installation_period_months} months</p>
              </div>
            )}
            {lease.lease_execution_date && (
              <div>
                <p className="text-gray-600">Execution Date</p>
                <p className="font-medium">
                  {format(new Date(lease.lease_execution_date), 'PPP')}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Property Details */}
      <Card>
        <CardHeader>
          <CardTitle>Property Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {lease.properties?.[0] ? (
            <div className="space-y-3 text-sm">
              <div>
                <p className="text-gray-600">Building Address</p>
                <p className="font-medium">{lease.properties[0].building_address || 'N/A'}</p>
              </div>
              <div>
                <p className="text-gray-600">Leased Premises</p>
                <p className="font-medium">{lease.properties[0].leased_premises_address || 'N/A'}</p>
              </div>
              <div>
                <p className="text-gray-600">Rental Area</p>
                <p className="font-medium">
                  {lease.properties[0].rental_area_sqft ? `${lease.properties[0].rental_area_sqft} sq ft` : 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-gray-600">Property Condition</p>
                <p className="font-medium">{lease.properties[0].property_condition || 'N/A'}</p>
              </div>
              {lease.properties[0].cadastre_details && (
                <div>
                  <p className="text-gray-600">Cadastre Details</p>
                  <p className="font-medium">{lease.properties[0].cadastre_details}</p>
                </div>
              )}
            </div>
          ) : (
            <p className="text-gray-500">No property details available</p>
          )}
        </CardContent>
      </Card>

      {/* Financial Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Financial Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {lease.financial_terms?.[0] ? (
            <div className="space-y-3 text-sm">
              <div>
                <p className="text-gray-600">Monthly Base Rent</p>
                <p className="font-medium text-lg">
                  {lease.financial_terms[0].rent_currency || 'CAD'} {lease.financial_terms[0].monthly_base_rent?.toLocaleString() || 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-gray-600">Annual Base Rent</p>
                <p className="font-medium">
                  {lease.financial_terms[0].rent_currency || 'CAD'} {lease.financial_terms[0].annual_base_rent?.toLocaleString() || 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-gray-600">Rent Per Sq Ft</p>
                <p className="font-medium">
                  {lease.financial_terms[0].annual_rent_per_sqft ? `${lease.financial_terms[0].rent_currency || 'CAD'} ${lease.financial_terms[0].annual_rent_per_sqft}` : 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-gray-600">Payment Frequency</p>
                <p className="font-medium">{lease.financial_terms[0].rent_payment_frequency || 'N/A'}</p>
              </div>
              {securityDeposit && (
                <div>
                  <p className="text-gray-600">Security Deposit</p>
                  <p className="font-medium">
                    {lease.financial_terms[0].rent_currency || 'CAD'} {securityDeposit.security_deposit_total?.toLocaleString() || 'N/A'}
                  </p>
                </div>
              )}
            </div>
          ) : (
            <p className="text-gray-500">No financial details available</p>
          )}
        </CardContent>
      </Card>

      {/* Signatures */}
      <Card>
        <CardHeader>
          <CardTitle>Signatures</CardTitle>
        </CardHeader>
        <CardContent>
          {signatures.length > 0 ? (
            <div className="space-y-3 text-sm">
              {landlordSignature && (
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium">Landlord</p>
                    <p className="text-gray-600">{landlordSignature.signatory_name}</p>
                    <p className="text-gray-500 text-xs">{landlordSignature.signatory_title}</p>
                  </div>
                  <p className="text-gray-500 text-xs">
                    {landlordSignature.execution_date ? format(new Date(landlordSignature.execution_date), 'MMM d, yyyy') : 'Not signed'}
                  </p>
                </div>
              )}
              {tenantSignature && (
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium">Tenant</p>
                    <p className="text-gray-600">{tenantSignature.signatory_name}</p>
                    <p className="text-gray-500 text-xs">{tenantSignature.signatory_title}</p>
                  </div>
                  <p className="text-gray-500 text-xs">
                    {tenantSignature.execution_date ? format(new Date(tenantSignature.execution_date), 'MMM d, yyyy') : 'Not signed'}
                  </p>
                </div>
              )}
              {signatures.filter((s: any) => !['landlord', 'tenant'].includes(s.party_type)).map((signature: any) => (
                <div key={signature.signature_id} className="flex justify-between items-start">
                  <div>
                    <p className="font-medium">{signature.party_type}</p>
                    <p className="text-gray-600">{signature.signatory_name}</p>
                    <p className="text-gray-500 text-xs">{signature.signatory_title}</p>
                  </div>
                  <p className="text-gray-500 text-xs">
                    {signature.execution_date ? format(new Date(signature.execution_date), 'MMM d, yyyy') : 'Not signed'}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">No signature information available</p>
          )}
        </CardContent>
      </Card>

      {/* Document Information */}
      <Card>
        <CardHeader>
          <CardTitle>Document Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            {lease.document_language && (
              <div>
                <p className="text-gray-600">Document Language</p>
                <p className="font-medium">{lease.document_language}</p>
              </div>
            )}
            {lease.version_number && (
              <div>
                <p className="text-gray-600">Version Number</p>
                <p className="font-medium">v{lease.version_number}</p>
              </div>
            )}
            {lease.installation_period_start && (
              <div>
                <p className="text-gray-600">Installation Start</p>
                <p className="font-medium">
                  {format(new Date(lease.installation_period_start), 'PPP')}
                </p>
              </div>
            )}
            {lease.installation_period_end && (
              <div>
                <p className="text-gray-600">Installation End</p>
                <p className="font-medium">
                  {format(new Date(lease.installation_period_end), 'PPP')}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
