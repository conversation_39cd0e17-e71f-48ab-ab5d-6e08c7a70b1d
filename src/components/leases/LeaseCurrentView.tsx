import { format } from 'date-fns';

interface LeaseCurrentViewProps {
  lease: any;
}

export const LeaseCurrentView = ({ lease }: LeaseCurrentViewProps) => {
  // Extract data from lease object
  const property = lease.properties?.[0];
  const tenant = lease.parties?.find((p: any) => p.party_type === 'tenant');
  const landlord = lease.parties?.find((p: any) => p.party_type === 'landlord');
  const financialTerms = lease.financial_terms?.[0];
  const securityDeposit = lease.security_deposits?.[0];
  const rentEscalations = lease.rent_escalations || [];
  const renewalOptions = lease.renewal_options || [];
  const tenantInducements = lease.tenant_inducements || [];
  const operatingCosts = lease.operating_costs_details?.[0];
  const useRestrictions = lease.use_restrictions || [];
  const insuranceTerms = lease.insurance_liability_terms || [];
  const assignmentTerms = lease.assignment_subletting_terms?.[0];
  const defaultRemedies = lease.default_remedies || [];
  const maintenanceObligations = lease.maintenance_obligations || [];
  const earlyTermination = lease.early_termination_options?.[0];
  const guarantor = lease.guarantors?.[0];
  const brokers = lease.brokers || [];
  const improvementTerms = lease.improvement_terms || [];
  
  // Get guarantor party details if exists
  const guarantorParty = guarantor ? lease.parties?.find((p: any) => p.party_id === guarantor.party_id) : null;
  
  // Extract data from current_view table
  const currentView = lease.current_view?.[0] || {};
  
  // Get broker info
  const broker = brokers[0];

  // Format dates
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch {
      return 'N/A';
    }
  };

  // Format currency
  const formatCurrency = (amount: number | null | undefined) => {
    if (amount === null || amount === undefined) return 'N/A';
    return `$${amount.toLocaleString('en-CA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  // Helper to get value or N/A
  const getValue = (value: any, isNumeric: boolean = false) => {
    if (value === null || value === undefined) return 'N/A';
    // For numeric fields, if the value is 0 or "0", return it as is.
    // Otherwise, if it's an empty string, return 'N/A'.
    if (isNumeric) {
      if (value === 0 || value === '0') return value;
    }
    if (value === '') return 'N/A'; // Treat empty string as N/A for all cases unless explicitly handled above for numeric 0
    return value;
  };
  
  // Helper to check if numeric value is zero (used for percentage display)
  const isZeroValue = (value: any) => {
    return value === 0 || value === '0'; // Only check for 0 or "0"
  };

  return (
    <div className="bg-white p-4 text-xs" style={{ fontFamily: 'Arial, sans-serif' }}>
      {/* Header Section */}
      <div className="border border-gray-400">
        <div className="flex">
          <div className="flex-1 bg-gray-100 p-4 text-center border-r border-gray-400">
            <h1 className="text-base font-bold mb-2">LEASE SUMMARY</h1>
            <p className="text-sm font-semibold">PROPERTY ADDRESS: {getValue(property?.building_address || property?.leased_premises_address)}</p>
            <p className="text-sm mt-1">Leasing Rep: {getValue(currentView?.prepared_by)}</p>
            {broker && <p className="text-sm mt-1">Broker: {getValue(broker.broker_name)} - {getValue(broker.company_name)}</p>}
          </div>
          <div className="w-64" style={{ backgroundColor: '#D4A574' }}>
            <div className="p-4 text-center">
              <p className="font-bold text-sm">PAD FORM (to be coordinated by Lease Admin)</p>
              <p className="text-xs mt-1">Date sent to T: {formatDate(currentView.pad_form_sent_date)}</p>
              <p className="text-xs">Date entered into Hopem: {formatDate(currentView.pad_form_entered_date)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Document Type, Parties & Effective Term and Approvals Section */}
      <div className="flex border-l border-r border-gray-400">
        <div className="flex-1 border-b border-gray-400">
          <div className="bg-gray-100 p-2 border-b border-gray-400">
            <h3 className="font-bold text-xs">LEASE DOCUMENTS</h3>
          </div>
          <div className="bg-gray-100 p-2 border-b border-gray-400">
            <h3 className="font-bold text-xs">DOCUMENT TYPE, PARTIES & EFFECTIVE TERM</h3>
          </div>
          <div className="p-3">
            <p className="mb-4">
              Lease ("L") dated {formatDate(lease.lease_execution_date)} between <strong>{getValue(landlord?.party_name)}</strong> ("LL") and{' '}
              <strong>{getValue(tenant?.party_name)}</strong> ("T") commencing on or around{' '}
              {formatDate(lease.commencement_date)} and terminating on the {lease.lease_term_years ? `${lease.lease_term_years} year` : lease.lease_term_duration || 'N/A'} anniversary of the Commencement Date, this date being on or around {formatDate(lease.end_date)}
            </p>
            
            <div className="mt-6 space-y-3">
              <div className="text-center py-2 border-b border-gray-300">Addendum/Amendment/Renewal</div>
              <div className="text-center py-2 border-b border-gray-300">Addendum/Amendment/Renewal</div>
              <div className="text-center py-2 border-b border-gray-300">Addendum/Amendment/Renewal</div>
              <div className="text-center py-2 border-b border-gray-300">Addendum/Amendment/Renewal</div>
            </div>
            
            <div className="mt-6 space-y-2">
              <div className="text-center py-1">Possession of Keys:</div>
              <div className="text-center py-1">Remeasurement:</div>
              <div className="text-center py-1">Termination:</div>
            </div>
          </div>
        </div>
        
        {/* Approvals Section */}
        <div className="w-96 border-l border-b border-gray-400">
          <div className="bg-gray-100 p-2 border-b border-gray-400">
            <h3 className="font-bold text-xs text-center">APPROVALS</h3>
          </div>
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-300">
                <th className="text-left p-2 w-24"></th>
                <th className="text-left p-2">Name</th>
                <th className="text-left p-2">Date</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-gray-300">
                <td className="p-2 font-semibold">Prepared by:</td>
                <td className="p-2">{getValue(currentView.prepared_by)}</td>
                <td className="p-2">{formatDate(lease.created_date)}</td>
              </tr>
              <tr className="border-b border-gray-300">
                <td className="p-2 font-semibold">Updated by:</td>
                <td className="p-2"></td>
                <td className="p-2"></td>
              </tr>
              <tr className="border-b border-gray-300">
                <td className="p-2 font-semibold">Updated by:</td>
                <td className="p-2"></td>
                <td className="p-2"></td>
              </tr>
              <tr className="border-b border-gray-300">
                <td className="p-2 font-semibold">Updated by:</td>
                <td className="p-2"></td>
                <td className="p-2"></td>
              </tr>
              <tr className="border-b border-gray-300">
                <td className="p-2 font-semibold">Updated by:</td>
                <td className="p-2"></td>
                <td className="p-2"></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Tenant Information */}
      <div className="border-l border-r border-b border-gray-400">
        <div className="bg-gray-100 p-2 border-b border-gray-400">
          <h3 className="font-bold text-xs text-center">TENANT</h3>
        </div>
        <table className="w-full">
          <tbody>
            <tr className="border-b border-gray-300">
              <td className="p-2 w-48 font-semibold">Tenant Legal name:</td>
              <td className="p-2">{getValue(tenant?.party_name)}</td>
            </tr>
            <tr className="border-b border-gray-300">
              <td className="p-2 font-semibold">Tenant Trade name:</td>
              <td className="p-2">{getValue(currentView.tenant_trade_name)}</td>
            </tr>
            <tr className="border-b border-gray-300">
              <td className="p-2 font-semibold">Language:</td>
              <td className="p-2">{getValue(currentView.tenant_preferred_language)}</td>
            </tr>
            <tr className="border-b border-gray-300">
              <td className="p-2 font-semibold">Contact Person:</td>
              <td className="p-2">{getValue(tenant?.representative_name)}</td>
            </tr>
            <tr className="border-b border-gray-300">
              <td className="p-2 font-semibold">Address for Notices:</td>
              <td className="p-2">{tenant?.address_street ? `${tenant.address_street}, ${tenant.address_city}, ${tenant.address_province} ${tenant.address_postal_code}` : 'N/A'}</td>
            </tr>
            <tr className="border-b border-gray-300">
              <td className="p-2 font-semibold">Telephone #:</td>
              <td className="p-2">{getValue(tenant?.phone_number)}</td>
            </tr>
            <tr className="border-b border-gray-300">
              <td className="p-2 font-semibold">Email:</td>
              <td className="p-2">{getValue(tenant?.email)}</td>
            </tr>
            <tr className="border-b border-gray-300">
              <td className="p-2 font-semibold">Guarantor (if any):</td>
              <td className="p-2">{guarantorParty ? getValue(guarantorParty.party_name) : 'N/A'}</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Description of Premises */}
      <div className="border-l border-r border-b border-gray-400">
        <div className="bg-gray-100 p-2 border-b border-gray-400">
          <h3 className="font-bold text-xs text-center">DESCRIPTION OF PREMISES</h3>
        </div>
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-400">
              <th className="border-r border-gray-300 p-2 text-left">FLOOR/SUITE</th>
              <th className="border-r border-gray-300 p-2 text-left">USABLE AREA (Sq. Ft.)</th>
              <th className="border-r border-gray-300 p-2 text-left">RENTABLE AREA (Sq. Ft.)</th>
              <th className="border-r border-gray-300 p-2 text-left">GROSS UP% (if any)</th>
              <th className="border-r border-gray-300 p-2 text-left">OFFICE/INDUSTRIAL/RETAIL/STORAGE</th>
              <th className="p-2 text-left">COMMENTS (Deemed, certified or estimated)</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b border-gray-300">
              <td className="border-r border-gray-300 p-2">{getValue(currentView.floor_suite_number || property?.leased_premises_address)}</td>
              <td className="border-r border-gray-300 p-2 text-center">{getValue(currentView.usable_area_sqft, true)}</td>
              <td className="border-r border-gray-300 p-2 text-center">{getValue(currentView.usable_area_sqft, true)}</td>
              <td className="border-r border-gray-300 p-2 text-center">{operatingCosts?.gross_up_occupancy_percentage ? `${operatingCosts.gross_up_occupancy_percentage}%` : 'N/A'}</td>
              <td className="border-r border-gray-300 p-2 text-center">{getValue(useRestrictions[0]?.restriction_type)}</td>
              <td className="p-2">{getValue(property?.measurement_method || property?.property_condition || useRestrictions[0]?.restriction_description)}</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Financial Terms */}
      <div className="border-l border-r border-b border-gray-400">
        <div className="bg-gray-100 p-2 border-b border-gray-400">
          <h3 className="font-bold text-xs text-center">FINANCIAL TERMS</h3>
        </div>
        <div className="p-4">
          <table className="w-full">
            <tbody>
              <tr>
                <td className="pb-3 pr-4 w-1/4 font-semibold">TYPE OF LEASE:</td>
                <td className="pb-3 pr-4 w-1/4">
                  <div className="border border-gray-400 p-1 text-center">{getValue(currentView.lease_type)}</div>
                </td>
                <td className="pb-3 pr-4 w-1/4 text-center">
                  <div className="border border-gray-400 p-1 text-center">{currentView.percentage_rent_applicable === true ? 'Percentage rent applicable' : currentView.percentage_rent_applicable === false ? 'No percentage rent' : 'N/A'}</div>
                </td>
                <td className="pb-3 w-1/4"></td>
              </tr>
              <tr>
                <td className="pb-3 pr-4 font-semibold">LANDLORD WORK:</td>
                <td className="pb-3" colSpan={3}>
                  <div className="border border-gray-400 p-1">
                    {getValue(
                      currentView.landlord_work_description || 
                      improvementTerms.find((i: any) => i.improvement_type === 'landlord_work')?.cost_responsibility
                    )}
                  </div>
                </td>
              </tr>
              <tr>
                <td className="pb-3 pr-4 font-semibold">POSSESSION DATE:</td>
                <td className="pb-3" colSpan={3}>
                  <div className="border border-gray-400 p-1">{formatDate(lease.possession_date)}</div>
                </td>
              </tr>
              <tr>
                <td className="pb-3 pr-4 font-semibold align-top">FIXTURING PERIOD:</td>
                <td className="pb-3" colSpan={3}>
                  <div className="border border-gray-400 p-1 min-h-[60px]">
                    {getValue(currentView.installation_period_description)}
                  </div>
                </td>
              </tr>
              <tr>
                <td className="pb-3 pr-4 font-semibold">UTILITIES DURING FIXTURING:</td>
                <td className="pb-3">
                  <div className="border border-gray-400 p-1 text-center">{getValue(currentView.utilities_during_fixturing)}</div>
                </td>
                <td className="pb-3"></td>
                <td className="pb-3"></td>
              </tr>
              <tr>
                <td className="pb-3 pr-4 font-semibold">OPEX/CAM DURING FIXTURING:</td>
                <td className="pb-3">
                  <div className="border border-gray-400 p-1 text-center">{getValue(currentView.opex_during_fixturing)}</div>
                </td>
                <td className="pb-3"></td>
                <td className="pb-3"></td>
              </tr>
              <tr>
                <td className="pb-3 pr-4 font-semibold">FREE RENT (if applicable)</td>
                <td className="pb-3" colSpan={3}>
                  <div className="border border-gray-400 p-1">{getValue(currentView.free_rent_payment_terms)}</div>
                </td>
              </tr>
            </tbody>
          </table>
          
          <table className="w-full mt-4">
            <tbody>
              <tr>
                <td className="pb-3 pr-4 w-1/4 font-semibold align-top">TENANT ALLOWANCE (TI)</td>
                <td className="pb-3" colSpan={2}>
                  <div className="border border-gray-400 p-1 min-h-[40px]">
                    {getValue(currentView.tenant_allowance_description)}
                  </div>
                </td>
              </tr>
              <tr>
                <td className="pb-3 pr-4 font-semibold align-top">COMMENCEMENT DATE</td>
                <td className="pb-3" colSpan={2}>
                  <div className="border border-gray-400 p-1 min-h-[100px]">
                    {formatDate(lease.commencement_date)}{currentView.commencement_conditions ? ` ${currentView.commencement_conditions}` : ''}
                  </div>
                </td>
              </tr>
              <tr>
                <td className="pb-3 pr-4 font-semibold">SECURITY DEPOSIT</td>
                <td className="pb-3" colSpan={2}>
                  <div className="border border-gray-400 p-1">
                    {getValue(currentView.security_deposit_terms_and_conditions)}
                  </div>
                </td>
              </tr>
              <tr>
                <td className="pb-3 pr-4 font-semibold">MOVABLE HYPOTHEC</td>
                <td className="pb-3" colSpan={2}>
                  <div className="border border-gray-400 p-1">{getValue(currentView.movable_hypothec)}</div>
                </td>
              </tr>
              <tr>
                <td className="pb-3 pr-4 font-semibold">PROPORTIONATE SHARE<br/>(if PS not specified in L - add calculation details as special provision)</td>
                <td className="pb-3">
                  <div className="flex gap-2">
                    <div className="border border-gray-400 p-1 flex-1">Operating Expenses:</div>
                    <div className="border border-gray-400 p-1 flex-1 text-center">
                      {getValue(currentView.proportionate_share_operating_expenses, true) !== 'N/A' 
                        ? `${getValue(currentView.proportionate_share_operating_expenses, true)}%` 
                        : (financialTerms?.admin_fee_on_operating_expenses_rate ? `${(financialTerms.admin_fee_on_operating_expenses_rate * 100).toFixed(0)}% admin fee` : 'N/A')}
                    </div>
                  </div>
                  <div className="flex gap-2 mt-2">
                    <div className="border border-gray-400 p-1 flex-1">Real Estate Taxes:</div>
                    <div className="border border-gray-400 p-1 flex-1 text-center">
                      {getValue(currentView.proportionate_share_taxes, true) !== 'N/A'
                        ? `${getValue(currentView.proportionate_share_taxes, true)}%` 
                        : (financialTerms?.admin_fee_on_taxes_rate ? `${(financialTerms.admin_fee_on_taxes_rate * 100).toFixed(0)}% admin fee` : 'N/A')}
                    </div>
                  </div>
                </td>
                <td className="pb-3 pl-4"></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Comments Section */}
      <div className="border-l border-r border-b border-gray-400">
        <div className="bg-gray-100 p-2 border-b border-gray-400">
          <h3 className="font-bold text-xs text-center">COMMENTS</h3>
        </div>
        <div className="p-4">
          <p className="text-xs">
            PROPORTIONATE SHARE - numerator is area in SF of Leased Premises and denominator is entire gross rentable area in SF of Building - L, Art. 1.1(w)
          </p>
        </div>
      </div>
    </div>
  );
};
