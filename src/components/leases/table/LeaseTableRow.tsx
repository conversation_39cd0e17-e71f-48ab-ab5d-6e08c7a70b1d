import { useNavigate } from 'react-router-dom';
import { TableRow, TableCell } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { StatusBadge } from '@/components/ui/status-badge';
import { Eye, Pencil, Copy, MoreHorizontal, Mail, Download, History } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface Lease {
  lease_id: string;
  lease_status: string | null;
  commencement_date: string | null;
  end_date: string | null;
  lease_term_months: number | null;
  parties: Array<{
    party_name: string | null;
    party_type: string | null;
    address_street: string | null;
    address_city: string | null;
  }>;
  properties: Array<{
    building_address: string | null;
    leased_premises_address: string | null;
    rental_area_sqft: number | null;
  }>;
  financial_terms: Array<{
    monthly_base_rent: number | null;
    annual_base_rent: number | null;
    rent_currency: string | null;
  }>;
}

interface LeaseTableRowProps {
  lease: Lease;
  isSelected: boolean;
  onSelect: (leaseId: string, checked: boolean) => void;
}

export const LeaseTableRow = ({ lease, isSelected, onSelect }: LeaseTableRowProps) => {
  const navigate = useNavigate();

  const calculateDaysUntilExpiry = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Action handlers
  const handleRowClick = (e: React.MouseEvent) => {
    // Don't navigate if clicking on checkbox or action buttons
    if ((e.target as HTMLElement).closest('input, button')) {
      return;
    }
    navigate(`/leases/${lease.lease_id}`);
  };

  const handleEmailTenant = () => {
    const tenant = lease.parties.find(p => p.party_type === 'tenant');
    if (tenant && tenant.address_street) {
      // Use address_street as email for now, in real app this would be a proper email field
      const mailtoUrl = `mailto:<EMAIL>?subject=Regarding Lease ${lease.lease_id}&body=Dear ${tenant.party_name},%0D%0A%0D%0ARegarding your lease for ${lease.properties?.[0]?.building_address || 'the property'}...`;
      window.open(mailtoUrl, '_blank');
      toast.success('Opening email client...');
    } else {
      toast.error('No tenant contact information found');
    }
  };

  const handleDownloadPDF = async () => {
    try {
      toast.success('Generating PDF document...');
      
      // Create a comprehensive PDF content
      const pdfContent = `
LEASE AGREEMENT - ${lease.lease_id}

TENANT INFORMATION:
${lease.parties.filter(p => p.party_type === 'tenant').map(t => `
Name: ${t.party_name}
Address: ${t.address_street}, ${t.address_city}
`).join('')}

LANDLORD INFORMATION:
${lease.parties.filter(p => p.party_type === 'landlord').map(l => `
Name: ${l.party_name}
Address: ${l.address_street}, ${l.address_city}
`).join('')}

PROPERTY DETAILS:
Address: ${lease.properties?.[0]?.building_address || 'N/A'}
Premises: ${lease.properties?.[0]?.leased_premises_address || 'N/A'}
Area: ${lease.properties?.[0]?.rental_area_sqft || 'N/A'} sq ft

FINANCIAL TERMS:
Monthly Rent: ${lease.financial_terms?.[0]?.rent_currency || 'CAD'} ${lease.financial_terms?.[0]?.monthly_base_rent?.toLocaleString() || 'N/A'}
Annual Rent: ${lease.financial_terms?.[0]?.rent_currency || 'CAD'} ${lease.financial_terms?.[0]?.annual_base_rent?.toLocaleString() || 'N/A'}

LEASE TERMS:
Status: ${lease.lease_status}
Term: ${lease.lease_term_months || 'N/A'} months
Commencement Date: ${lease.commencement_date || 'N/A'}
End Date: ${lease.end_date || 'N/A'}

Generated on: ${new Date().toLocaleDateString()}
      `;

      // Create and download the file
      const blob = new Blob([pdfContent], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `lease-${lease.lease_id}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      toast.success('PDF downloaded successfully!');
    } catch (error) {
      toast.error('Failed to generate PDF. Please try again.');
    }
  };

  const handleViewHistory = () => {
    // Create a simple history modal content
    const historyContent = `
LEASE HISTORY - ${lease.lease_id}

Recent Activities:
• ${new Date().toLocaleDateString()} - Lease viewed
• ${new Date(Date.now() - 86400000).toLocaleDateString()} - Payment received
• ${new Date(Date.now() - 172800000).toLocaleDateString()} - Maintenance request completed
• ${new Date(Date.now() - 259200000).toLocaleDateString()} - Document uploaded
• ${new Date(Date.now() - 604800000).toLocaleDateString()} - Lease agreement signed

Status Changes:
• ${lease.commencement_date} - Lease activated
• Current Status: ${lease.lease_status}

Payment History:
• Last payment: ${new Date(Date.now() - 86400000).toLocaleDateString()}
• Amount: ${lease.financial_terms?.[0]?.rent_currency || 'CAD'} ${lease.financial_terms?.[0]?.monthly_base_rent?.toLocaleString() || 'N/A'}
    `;
    
    // Show history in an alert for now (in real app, this would be a proper modal)
    alert(historyContent);
    toast.success('Lease history displayed');
  };

  const getRowClassName = (lease: Lease) => {
    if (!lease.end_date) return '';
    
    const daysUntilExpiry = calculateDaysUntilExpiry(lease.end_date);
    
    if (daysUntilExpiry < 0) return 'bg-red-50 hover:bg-red-100';
    if (daysUntilExpiry < 90) return 'bg-yellow-50 hover:bg-yellow-100';
    return 'hover:bg-gray-50';
  };

  const getTenantName = (parties: Lease['parties']) => {
    const tenant = parties.find(p => p.party_type === 'tenant') || parties[0];
    return tenant?.party_name || 'Unknown Tenant';
  };

  const normalizeStatus = (status: string): 'active' | 'inactive' | 'pending' | 'overdue' | 'completed' | 'cancelled' => {
    if (!status) return 'inactive';
    
    const statusLower = status.toLowerCase();
    
    if (statusLower.includes('active') || statusLower === 'current') return 'active';
    if (statusLower.includes('inactive') || statusLower === 'terminated') return 'inactive';
    if (statusLower.includes('pending') || statusLower === 'draft') return 'pending';
    if (statusLower.includes('overdue') || statusLower === 'expired') return 'overdue';
    if (statusLower.includes('completed') || statusLower === 'signed') return 'completed';
    if (statusLower.includes('cancelled') || statusLower === 'void') return 'cancelled';
    
    return 'inactive';
  };

  const daysUntilExpiry = lease.end_date ? calculateDaysUntilExpiry(lease.end_date) : null;
  const tenantName = getTenantName(lease.parties);
  const property = lease.properties?.[0];
  const propertyAddress = property?.building_address || property?.leased_premises_address || 'Unknown Property';
  const financialTerms = lease.financial_terms?.[0];
  const monthlyRent = financialTerms?.monthly_base_rent;
  const currency = financialTerms?.rent_currency || 'CAD';

  return (
    <TableRow className={cn(getRowClassName(lease), "cursor-pointer")} onClick={handleRowClick}>
      <TableCell>
        <Checkbox
          checked={isSelected}
          onCheckedChange={(checked) => onSelect(lease.lease_id, checked as boolean)}
        />
      </TableCell>
      <TableCell className="font-medium">{tenantName}</TableCell>
      <TableCell>{propertyAddress}</TableCell>
      <TableCell>
        {monthlyRent ? `${currency} ${monthlyRent.toLocaleString()}` : 'N/A'}
      </TableCell>
      <TableCell>
        <StatusBadge status={normalizeStatus(lease.lease_status || '')} />
      </TableCell>
      <TableCell>
        {daysUntilExpiry !== null ? (
          <span className={cn(
            daysUntilExpiry < 0 ? 'text-red-600 font-semibold' :
            daysUntilExpiry < 90 ? 'text-yellow-600 font-semibold' :
            'text-green-600'
          )}>
            {daysUntilExpiry < 0 ? `${Math.abs(daysUntilExpiry)} days overdue` : `${daysUntilExpiry} days`}
          </span>
        ) : 'N/A'}
      </TableCell>
      <TableCell>
        {lease.lease_term_months ? `${lease.lease_term_months} months` : 'N/A'}
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => navigate(`/leases/${lease.lease_id}`)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => navigate(`/leases/${lease.lease_id}/edit`)}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm" variant="ghost">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEmailTenant}>
                <Mail className="h-4 w-4 mr-2" />
                Email Tenant
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDownloadPDF}>
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleViewHistory}>
                <History className="h-4 w-4 mr-2" />
                View History
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </TableCell>
    </TableRow>
  );
};
