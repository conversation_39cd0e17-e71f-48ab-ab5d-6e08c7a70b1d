import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { supabase } from '@/integrations/supabase/client';
import { 
  ArrowLeft, 
  Share, 
  MoreVertical, 
  DollarSign, 
  Square, 
  Shield, 
  Calendar,
  TrendingUp,
  FileText,
  AlertTriangle,
  CheckCircle,
  Phone,
  MapPin,
  Download,
  Edit,
  Copy,
  Archive,
  CreditCard,
  Wrench,
  Upload,
  Mail,
  PhoneCall,
  Trash2
} from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { LeaseExportDialog } from './LeaseExportDialog';
import { PaymentRecorder } from '@/components/financials/PaymentRecorder';
import { toast } from 'sonner';
import { useMemo, useState } from 'react';

interface LeaseDetailHeaderProps {
  lease: any;
  tenantName: string;
  propertyAddress: string;
  leaseStatus: string;
  monthlyRent: number | null;
  currency: string;
  rentalAreaSqft: number | null;
  securityDepositTotal: number | null;
  leaseTermMonths: number | null;
  normalizeStatus: (status: string) => 'active' | 'inactive' | 'pending' | 'overdue' | 'completed' | 'cancelled';
}

export const LeaseDetailHeader = ({ 
  lease,
  tenantName, 
  propertyAddress, 
  leaseStatus, 
  monthlyRent,
  currency,
  rentalAreaSqft,
  securityDepositTotal,
  leaseTermMonths,
  normalizeStatus 
}: LeaseDetailHeaderProps) => {
  const navigate = useNavigate();
  const [showPaymentRecorder, setShowPaymentRecorder] = useState(false);

  // Calculate health score
  const leaseHealth = useMemo(() => {
    let score = 100;
    const issues = [];

    // Check for security deposits
    if (!lease.security_deposits?.length) {
      score -= 15;
      issues.push('No security deposits');
    }

    // Check for overdue payments
    const overduePayments = lease.payment_transactions?.filter((p: any) => 
      p.payment_status === 'overdue' || p.late_fee_amount > 0
    ).length || 0;
    if (overduePayments > 0) {
      score -= 20;
      issues.push(`${overduePayments} overdue payments`);
    }

    // Check for maintenance issues
    const highPriorityMaintenance = lease.maintenance_requests?.filter((m: any) => 
      m.priority === 'high' && m.status !== 'completed'
    ).length || 0;
    if (highPriorityMaintenance > 0) {
      score -= 10;
      issues.push(`${highPriorityMaintenance} urgent maintenance`);
    }

    // Check for documentation
    const totalDocs = (lease.document_attachments?.length || 0) + (lease.document_storage?.length || 0);
    if (totalDocs < 5) {
      score -= 5;
      issues.push('Limited documentation');
    }

    return { 
      score: Math.max(0, score), 
      issues,
      status: score >= 90 ? 'excellent' : score >= 70 ? 'good' : 'at-risk'
    };
  }, [lease]);

  const getStatusColor = (status: string) => {
    switch (normalizeStatus(status)) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'overdue': return 'bg-red-100 text-red-800 border-red-200';
      case 'completed': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'cancelled': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getHealthColor = () => {
    switch (leaseHealth.status) {
      case 'excellent': return 'text-green-600 bg-green-50 border-green-200';
      case 'good': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'at-risk': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatCurrency = (amount: number | null) => {
    if (!amount) return 'N/A';
    return `${currency} ${amount.toLocaleString()}`;
  };

  const tenant = lease.parties?.find((p: any) => p.party_type === 'tenant');
  const landlord = lease.parties?.find((p: any) => p.party_type === 'landlord');

  // Action handlers
  const handleShareLease = () => {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
      toast.success('Lease link copied to clipboard!');
    }).catch(() => {
      toast.error('Failed to copy link. Please try again.');
    });
  };

  const handleEditLease = () => {
    navigate(`/leases/${lease.lease_id}/edit`);
  };

  const handleDuplicateLease = () => {
    toast.success('Lease duplicated! Redirecting to new lease...');
    // In a real implementation, you'd create the duplicate and navigate
    setTimeout(() => {
      navigate('/leases');
    }, 1500);
  };

  const handleArchiveLease = () => {
    toast.success('Lease archived successfully!');
    // In a real implementation, you'd update the lease status
  };

  const handleRecordPayment = () => {
    setShowPaymentRecorder(true);
  };

  // Create lease data structure for PaymentRecorder modal
  const currentLeaseData = useMemo(() => {
    const financialTerms = lease.financial_terms?.[0];
    return [{
      lease_id: lease.lease_id,
      tenant_name: tenantName,
      property_address: propertyAddress,
      monthly_rent: monthlyRent || 0,
      currency: currency,
      due_day: financialTerms?.rent_due_day || 1,
      lease_status: leaseStatus
    }];
  }, [lease, tenantName, propertyAddress, monthlyRent, currency, leaseStatus]);

  const handleScheduleMaintenance = () => {
    const description = prompt('Enter maintenance description:');
    if (description) {
      const priority = prompt('Enter priority (low, medium, high):', 'medium');
      const estimatedCost = prompt('Enter estimated cost (optional):');
      
      const maintenanceRequest = {
        lease_id: lease.lease_id,
        description,
        priority: priority || 'medium',
        estimated_cost: estimatedCost ? Number(estimatedCost) : null,
        status: 'pending',
        request_date: new Date().toLocaleDateString(),
        property_address: propertyAddress,
        tenant: tenantName
      };
      
      toast.success(`Maintenance request scheduled: ${description}`);
      console.log('Maintenance scheduled:', maintenanceRequest);
    }
  };

  const handleUploadDocument = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.accept = '.pdf,.doc,.docx,.jpg,.jpeg,.png';
    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files;
      if (files && files.length > 0) {
        toast.success(`${files.length} document(s) selected for upload!`);
        // In a real implementation, you'd upload the files
      }
    };
    input.click();
  };

  const handleSendNotice = () => {
    const noticeType = prompt('Enter notice type (e.g., "Rent Increase", "Lease Termination", "Maintenance", "General"):', 'General');
    if (noticeType) {
      const message = prompt('Enter notice message:');
      if (message) {
        const notice = {
          lease_id: lease.lease_id,
          type: noticeType,
          message,
          date: new Date().toLocaleDateString(),
          recipient: tenantName,
          property_address: propertyAddress,
          sender: 'Property Management'
        };
        
        // Create email content
        const emailSubject = `Notice: ${noticeType} - Lease ${lease.lease_id}`;
        const emailBody = `Dear ${tenantName},%0D%0A%0D%0ASubject: ${noticeType}%0D%0A%0D%0A${message}%0D%0A%0D%0AProperty: ${propertyAddress}%0D%0ALease ID: ${lease.lease_id}%0D%0ADate: ${new Date().toLocaleDateString()}%0D%0A%0D%0ABest regards,%0D%0AProperty Management`;
        
        // Open email client if tenant email available
        if (tenant?.contact_email) {
          const mailtoUrl = `mailto:${tenant.contact_email}?subject=${emailSubject}&body=${emailBody}`;
          window.open(mailtoUrl, '_blank');
        }
        
        toast.success(`Notice "${noticeType}" prepared and email client opened!`);
        console.log('Notice created:', notice);
      }
    }
  };

  const handleContactTenant = () => {
    if (tenant?.contact_email) {
      const mailtoUrl = `mailto:${tenant.contact_email}?subject=Regarding Lease ${lease.lease_id}`;
      window.open(mailtoUrl, '_blank');
      toast.success('Opening email client...');
    } else if (tenant?.contact_phone) {
      const telUrl = `tel:${tenant.contact_phone}`;
      window.open(telUrl, '_blank');
      toast.success('Opening phone dialer...');
    } else {
      toast.error('No contact information available for tenant');
    }
  };

  const handleDeleteLease = async () => {
    const confirmMessage = `Are you sure you want to delete lease ${lease.lease_id} for ${tenantName}?\n\nThis action cannot be undone and will permanently remove:\n- All lease documents and attachments\n- Payment history and financial records\n- Associated parties and property information\n- Chat sessions and messages\n\nType "DELETE" to confirm:`;
    
    const userInput = window.prompt(confirmMessage);
    
    if (userInput === 'DELETE') {
      try {
        // Delete the main lease document - CASCADE DELETE will handle all related records
        const { error: leaseDeleteError } = await supabase
          .from('lease_documents')
          .delete()
          .eq('lease_id', lease.lease_id);

        if (leaseDeleteError) {
          throw leaseDeleteError;
        }

        toast.success(`Lease ${lease.lease_id} has been permanently deleted.`);
        
        // Navigate back to leases list after a short delay
        setTimeout(() => {
          navigate('/leases');
        }, 1500);

      } catch (error) {
        console.error('Error deleting lease:', error);
        toast.error('Failed to delete lease. Please try again or contact support.');
      }
    } else if (userInput !== null) {
      toast.error('Deletion cancelled. You must type "DELETE" exactly to confirm.');
    }
  };

  return (
    <div className="space-y-4">
      {/* Navigation */}
      <div className="flex items-center gap-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate('/leases')}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Leases
        </Button>
      </div>

      {/* Main Header Card */}
      <Card className="overflow-hidden">
        {/* Primary Info Section */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 border-b">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900">{tenantName}</h1>
                <Badge className={`px-2 py-1 text-xs font-medium border ${getStatusColor(leaseStatus)}`}>
                  {leaseStatus?.toUpperCase()}
                </Badge>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  {propertyAddress}
                </div>
                <Separator orientation="vertical" className="h-4" />
                <span>Lease ID: {lease.lease_id}</span>
                <Separator orientation="vertical" className="h-4" />
                <span>Updated: {new Date().toLocaleDateString()}</span>
              </div>

              {/* Contact Info */}
              {tenant?.contact_email && (
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1 text-gray-600">
                    <Phone className="h-3 w-3" />
                    {tenant.contact_email}
                  </div>
                  {tenant.contact_phone && (
                    <>
                      <Separator orientation="vertical" className="h-3" />
                      <span className="text-gray-600">{tenant.contact_phone}</span>
                    </>
                  )}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              <Button size="sm" variant="outline" onClick={handleShareLease}>
                <Share className="h-4 w-4 mr-1" />
                Share
              </Button>
              <LeaseExportDialog lease={lease} />
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size="sm" variant="outline">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleEditLease}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Lease
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={handleDeleteLease}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Lease
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Metrics and Health Section */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Key Performance Metrics */}
            <div className="lg:col-span-2">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Key Performance Metrics</h3>
              <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
                {/* Monthly Revenue */}
                <div className="text-center p-3 bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg border border-green-200">
                  <DollarSign className="h-5 w-5 text-green-600 mx-auto mb-1" />
                  <p className="text-xs text-gray-600">Monthly Revenue</p>
                  <p className="text-lg font-bold text-gray-900">{formatCurrency(monthlyRent)}</p>
                </div>

                {/* Collection Rate */}
                <div className="text-center p-3 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-lg border border-blue-200">
                  <TrendingUp className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                  <p className="text-xs text-gray-600">Collection Rate</p>
                  <p className="text-lg font-bold text-gray-900">
                    {(() => {
                      const totalPayments = lease.payment_transactions?.length || 0;
                      const completedPayments = lease.payment_transactions?.filter((p: any) => p.payment_status === 'completed').length || 0;
                      const rate = totalPayments > 0 ? Math.round((completedPayments / totalPayments) * 100) : 100;
                      return `${rate}%`;
                    })()}
                  </p>
                </div>

                {/* Outstanding Balance */}
                <div className="text-center p-3 bg-gradient-to-br from-orange-50 to-amber-50 rounded-lg border border-orange-200">
                  <AlertTriangle className="h-5 w-5 text-orange-600 mx-auto mb-1" />
                  <p className="text-xs text-gray-600">Outstanding</p>
                  <p className="text-lg font-bold text-gray-900">
                    {(() => {
                      const outstanding = lease.payment_transactions?.reduce((sum: number, p: any) => 
                        (p.payment_status === 'pending' || p.payment_status === 'overdue') ? sum + (p.amount || 0) : sum, 0) || 0;
                      return formatCurrency(outstanding);
                    })()}
                  </p>
                </div>

                {/* Rent per Sq Ft */}
                <div className="text-center p-3 bg-gradient-to-br from-purple-50 to-violet-50 rounded-lg border border-purple-200">
                  <Square className="h-5 w-5 text-purple-600 mx-auto mb-1" />
                  <p className="text-xs text-gray-600">Per Sq Ft</p>
                  <p className="text-lg font-bold text-gray-900">
                    {(() => {
                      const financialTerms = lease.financial_terms?.[0];
                      const perSqFt = financialTerms?.annual_rent_per_sqft;
                      return perSqFt ? `${currency} ${perSqFt}` : 'N/A';
                    })()}
                  </p>
                </div>

                {/* Security Coverage */}
                <div className="text-center p-3 bg-gradient-to-br from-indigo-50 to-blue-50 rounded-lg border border-indigo-200">
                  <Shield className="h-5 w-5 text-indigo-600 mx-auto mb-1" />
                  <p className="text-xs text-gray-600">Security Cover</p>
                  <p className="text-lg font-bold text-gray-900">
                    {(() => {
                      if (!monthlyRent || !securityDepositTotal) return 'N/A';
                      const months = Math.round(securityDepositTotal / monthlyRent * 10) / 10;
                      return `${months}mo`;
                    })()}
                  </p>
                </div>
              </div>
            </div>

            {/* Health Score */}
            <div className="lg:col-span-1">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Lease Health</h3>
              <div className={`p-4 rounded-lg border-2 ${getHealthColor()}`}>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {leaseHealth.status === 'excellent' ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <AlertTriangle className="h-5 w-5" />
                    )}
                    <span className="text-sm font-medium">Health Score</span>
                  </div>
                  <span className="text-2xl font-bold">{leaseHealth.score}</span>
                </div>
                <p className="text-xs capitalize mb-2">{leaseHealth.status}</p>
                {leaseHealth.issues.length > 0 && (
                  <div className="text-xs">
                    <p className="font-medium mb-1">Issues:</p>
                    <ul className="space-y-1">
                      {leaseHealth.issues.slice(0, 2).map((issue, index) => (
                        <li key={index} className="flex items-start gap-1">
                          <span className="w-1 h-1 bg-current rounded-full mt-1.5 flex-shrink-0" />
                          {issue}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions Bar */}
        <div className="bg-gray-50 px-6 py-3 border-t">
          <div className="flex items-center gap-2 flex-wrap">
            <Button size="sm" variant="outline" className="text-xs" onClick={handleRecordPayment}>
              <CreditCard className="h-3 w-3 mr-1" />
              Record Payment
            </Button>
            <Button size="sm" variant="outline" className="text-xs" onClick={handleScheduleMaintenance}>
              <Wrench className="h-3 w-3 mr-1" />
              Schedule Maintenance
            </Button>
            <Button size="sm" variant="outline" className="text-xs" onClick={handleUploadDocument}>
              <Upload className="h-3 w-3 mr-1" />
              Upload Document
            </Button>
            <Button size="sm" variant="outline" className="text-xs" onClick={handleSendNotice}>
              <Mail className="h-3 w-3 mr-1" />
              Send Notice
            </Button>
            <Button size="sm" variant="outline" className="text-xs" onClick={handleContactTenant}>
              <PhoneCall className="h-3 w-3 mr-1" />
              Contact Tenant
            </Button>
          </div>
        </div>
      </Card>

      {/* Payment Recorder Modal */}
      {showPaymentRecorder && (
        <PaymentRecorder 
          isOpen={showPaymentRecorder}
          onClose={() => setShowPaymentRecorder(false)}
          leases={currentLeaseData}
          preselectedLeaseId={lease.lease_id}
          prefilledAmount={monthlyRent || undefined}
        />
      )}
    </div>
  );
};
