export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      access_inspection_rights: {
        Row: {
          access_id: string
          access_rights_description: string | null
          access_type: string | null
          lease_id: string
          notice_requirements: string | null
          time_restrictions: string | null
        }
        Insert: {
          access_id?: string
          access_rights_description?: string | null
          access_type?: string | null
          lease_id: string
          notice_requirements?: string | null
          time_restrictions?: string | null
        }
        Update: {
          access_id?: string
          access_rights_description?: string | null
          access_type?: string | null
          lease_id?: string
          notice_requirements?: string | null
          time_restrictions?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "access_inspection_rights_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      assignment_subletting_terms: {
        Row: {
          admin_fee: number | null
          assignment_id: string
          consent_required: boolean | null
          information_requirements: string | null
          landlord_recapture_right: boolean | null
          lease_id: string
          refusal_reasons: string | null
          response_time_days: number | null
          transfer_type: string | null
        }
        Insert: {
          admin_fee?: number | null
          assignment_id?: string
          consent_required?: boolean | null
          information_requirements?: string | null
          landlord_recapture_right?: boolean | null
          lease_id: string
          refusal_reasons?: string | null
          response_time_days?: number | null
          transfer_type?: string | null
        }
        Update: {
          admin_fee?: number | null
          assignment_id?: string
          consent_required?: boolean | null
          information_requirements?: string | null
          landlord_recapture_right?: boolean | null
          lease_id?: string
          refusal_reasons?: string | null
          response_time_days?: number | null
          transfer_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "assignment_subletting_terms_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      chat_sessions: {
        Row: {
          created_at: string
          id: string
          lease_id: string | null
          metadata: Json | null
          title: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          lease_id?: string | null
          metadata?: Json | null
          title?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          lease_id?: string | null
          metadata?: Json | null
          title?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "chat_sessions_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
          {
            foreignKeyName: "chat_sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      compliance_requirements: {
        Row: {
          compliance_description: string | null
          compliance_id: string
          compliance_type: string | null
          lease_id: string
          penalty_terms: string | null
          responsibility_party: string | null
        }
        Insert: {
          compliance_description?: string | null
          compliance_id?: string
          compliance_type?: string | null
          lease_id: string
          penalty_terms?: string | null
          responsibility_party?: string | null
        }
        Update: {
          compliance_description?: string | null
          compliance_id?: string
          compliance_type?: string | null
          lease_id?: string
          penalty_terms?: string | null
          responsibility_party?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "compliance_requirements_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      default_remedies: {
        Row: {
          accelerated_rent_months: number | null
          cure_period_days: number | null
          default_description: string | null
          default_id: string
          default_type: string | null
          interest_calculation: string | null
          lease_id: string
          remedy_description: string | null
        }
        Insert: {
          accelerated_rent_months?: number | null
          cure_period_days?: number | null
          default_description?: string | null
          default_id?: string
          default_type?: string | null
          interest_calculation?: string | null
          lease_id: string
          remedy_description?: string | null
        }
        Update: {
          accelerated_rent_months?: number | null
          cure_period_days?: number | null
          default_description?: string | null
          default_id?: string
          default_type?: string | null
          interest_calculation?: string | null
          lease_id?: string
          remedy_description?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "default_remedies_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      destruction_expropriation_terms: {
        Row: {
          cost_responsibility: string | null
          destruction_id: string
          event_type: string | null
          lease_id: string
          rent_abatement_provisions: string | null
          repair_timeline_days: number | null
          termination_rights: string | null
        }
        Insert: {
          cost_responsibility?: string | null
          destruction_id?: string
          event_type?: string | null
          lease_id: string
          rent_abatement_provisions?: string | null
          repair_timeline_days?: number | null
          termination_rights?: string | null
        }
        Update: {
          cost_responsibility?: string | null
          destruction_id?: string
          event_type?: string | null
          lease_id?: string
          rent_abatement_provisions?: string | null
          repair_timeline_days?: number | null
          termination_rights?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "destruction_expropriation_terms_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      document_attachments: {
        Row: {
          annex_reference: string | null
          attachment_description: string | null
          attachment_id: string
          attachment_name: string | null
          attachment_type: string | null
          lease_id: string
        }
        Insert: {
          annex_reference?: string | null
          attachment_description?: string | null
          attachment_id?: string
          attachment_name?: string | null
          attachment_type?: string | null
          lease_id: string
        }
        Update: {
          annex_reference?: string | null
          attachment_description?: string | null
          attachment_id?: string
          attachment_name?: string | null
          attachment_type?: string | null
          lease_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "document_attachments_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      document_sections: {
        Row: {
          content: string
          created_at: string | null
          doc_order: number
          document_id: string
          id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          doc_order: number
          document_id: string
          id?: string
        }
        Update: {
          content?: string
          created_at?: string | null
          doc_order?: number
          document_id?: string
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "document_sections_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
        ]
      }
      documents: {
        Row: {
          complete_content: string | null
          created_at: string | null
          document_date: string | null
          document_language:
            | Database["public"]["Enums"]["document_language"]
            | null
          document_type: string | null
          id: string
          name: string
        }
        Insert: {
          complete_content?: string | null
          created_at?: string | null
          document_date?: string | null
          document_language?:
            | Database["public"]["Enums"]["document_language"]
            | null
          document_type?: string | null
          id?: string
          name: string
        }
        Update: {
          complete_content?: string | null
          created_at?: string | null
          document_date?: string | null
          document_language?:
            | Database["public"]["Enums"]["document_language"]
            | null
          document_type?: string | null
          id?: string
          name?: string
        }
        Relationships: []
      }
      expiration_holdover_terms: {
        Row: {
          expiration_id: string
          holdover_rent_percentage: number | null
          holdover_terms: string | null
          lease_id: string
          property_return_condition: string | null
          removal_obligations: string | null
          tacit_renewal_excluded: boolean | null
        }
        Insert: {
          expiration_id?: string
          holdover_rent_percentage?: number | null
          holdover_terms?: string | null
          lease_id: string
          property_return_condition?: string | null
          removal_obligations?: string | null
          tacit_renewal_excluded?: boolean | null
        }
        Update: {
          expiration_id?: string
          holdover_rent_percentage?: number | null
          holdover_terms?: string | null
          lease_id?: string
          property_return_condition?: string | null
          removal_obligations?: string | null
          tacit_renewal_excluded?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "expiration_holdover_terms_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      financial_terms: {
        Row: {
          administrative_fee_percentage: number | null
          annual_base_rent: number | null
          annual_rent_per_sqft: number | null
          assignment_admin_fee: number | null
          authorized_transfer_admin_fee: number | null
          estimated_operating_expenses_per_sqft: number | null
          estimated_taxes_per_sqft: number | null
          financial_id: string
          gst_rate: number | null
          interest_rate_formula: string | null
          late_payment_fee: number | null
          late_payment_threshold: number | null
          lease_id: string
          monthly_base_rent: number | null
          proportional_share_calculation: string | null
          qst_rate: number | null
          rent_currency: string | null
          rent_due_day: number | null
          rent_payment_frequency: string | null
          rent_payment_terms: string | null
        }
        Insert: {
          administrative_fee_percentage?: number | null
          annual_base_rent?: number | null
          annual_rent_per_sqft?: number | null
          assignment_admin_fee?: number | null
          authorized_transfer_admin_fee?: number | null
          estimated_operating_expenses_per_sqft?: number | null
          estimated_taxes_per_sqft?: number | null
          financial_id?: string
          gst_rate?: number | null
          interest_rate_formula?: string | null
          late_payment_fee?: number | null
          late_payment_threshold?: number | null
          lease_id: string
          monthly_base_rent?: number | null
          proportional_share_calculation?: string | null
          qst_rate?: number | null
          rent_currency?: string | null
          rent_due_day?: number | null
          rent_payment_frequency?: string | null
          rent_payment_terms?: string | null
        }
        Update: {
          administrative_fee_percentage?: number | null
          annual_base_rent?: number | null
          annual_rent_per_sqft?: number | null
          assignment_admin_fee?: number | null
          authorized_transfer_admin_fee?: number | null
          estimated_operating_expenses_per_sqft?: number | null
          estimated_taxes_per_sqft?: number | null
          financial_id?: string
          gst_rate?: number | null
          interest_rate_formula?: string | null
          late_payment_fee?: number | null
          late_payment_threshold?: number | null
          lease_id?: string
          monthly_base_rent?: number | null
          proportional_share_calculation?: string | null
          qst_rate?: number | null
          rent_currency?: string | null
          rent_due_day?: number | null
          rent_payment_frequency?: string | null
          rent_payment_terms?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "financial_terms_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      force_majeure_provisions: {
        Row: {
          effect_on_obligations: string | null
          event_description: string | null
          event_type: string | null
          force_majeure_id: string
          lease_id: string
          rent_payment_exemption: boolean | null
        }
        Insert: {
          effect_on_obligations?: string | null
          event_description?: string | null
          event_type?: string | null
          force_majeure_id?: string
          lease_id: string
          rent_payment_exemption?: boolean | null
        }
        Update: {
          effect_on_obligations?: string | null
          event_description?: string | null
          event_type?: string | null
          force_majeure_id?: string
          lease_id?: string
          rent_payment_exemption?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "force_majeure_provisions_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      health_emergency_provisions: {
        Row: {
          health_emergency_id: string
          landlord_rights: string | null
          lease_id: string
          liability_exclusions: string | null
          provision_description: string | null
          provision_type: string | null
        }
        Insert: {
          health_emergency_id?: string
          landlord_rights?: string | null
          lease_id: string
          liability_exclusions?: string | null
          provision_description?: string | null
          provision_type?: string | null
        }
        Update: {
          health_emergency_id?: string
          landlord_rights?: string | null
          lease_id?: string
          liability_exclusions?: string | null
          provision_description?: string | null
          provision_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "health_emergency_provisions_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      improvement_terms: {
        Row: {
          admin_fee_percentage: number | null
          approval_required: boolean | null
          contractor_restrictions: string | null
          cost_responsibility: string | null
          improvement_id: string
          improvement_type: string | null
          lease_id: string
          ownership_terms: string | null
          quality_requirements: string | null
        }
        Insert: {
          admin_fee_percentage?: number | null
          approval_required?: boolean | null
          contractor_restrictions?: string | null
          cost_responsibility?: string | null
          improvement_id?: string
          improvement_type?: string | null
          lease_id: string
          ownership_terms?: string | null
          quality_requirements?: string | null
        }
        Update: {
          admin_fee_percentage?: number | null
          approval_required?: boolean | null
          contractor_restrictions?: string | null
          cost_responsibility?: string | null
          improvement_id?: string
          improvement_type?: string | null
          lease_id?: string
          ownership_terms?: string | null
          quality_requirements?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "improvement_terms_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      insurance_liability_terms: {
        Row: {
          exclusions: string | null
          insurance_id: string
          lease_id: string
          provision_description: string | null
          provision_type: string | null
          responsible_party: string | null
        }
        Insert: {
          exclusions?: string | null
          insurance_id?: string
          lease_id: string
          provision_description?: string | null
          provision_type?: string | null
          responsible_party?: string | null
        }
        Update: {
          exclusions?: string | null
          insurance_id?: string
          lease_id?: string
          provision_description?: string | null
          provision_type?: string | null
          responsible_party?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "insurance_liability_terms_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      lease_documents: {
        Row: {
          commencement_date: string | null
          created_date: string | null
          document_id: string | null
          end_date: string | null
          installation_period_end: string | null
          installation_period_months: number | null
          installation_period_start: string | null
          lease_id: string
          lease_status: string | null
          lease_term_duration: string | null
          lease_term_months: number | null
          lease_term_years: number | null
          modified_date: string | null
          possession_date: string | null
          version_number: number | null
        }
        Insert: {
          commencement_date?: string | null
          created_date?: string | null
          document_id?: string | null
          end_date?: string | null
          installation_period_end?: string | null
          installation_period_months?: number | null
          installation_period_start?: string | null
          lease_id?: string
          lease_status?: string | null
          lease_term_duration?: string | null
          lease_term_months?: number | null
          lease_term_years?: number | null
          modified_date?: string | null
          possession_date?: string | null
          version_number?: number | null
        }
        Update: {
          commencement_date?: string | null
          created_date?: string | null
          document_id?: string | null
          end_date?: string | null
          installation_period_end?: string | null
          installation_period_months?: number | null
          installation_period_start?: string | null
          lease_id?: string
          lease_status?: string | null
          lease_term_duration?: string | null
          lease_term_months?: number | null
          lease_term_years?: number | null
          modified_date?: string | null
          possession_date?: string | null
          version_number?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "lease_documents_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
        ]
      }
      legal_general_provisions: {
        Row: {
          jurisdiction: string | null
          lease_id: string
          legal_id: string
          provision_description: string | null
          provision_type: string | null
          requirements: string | null
        }
        Insert: {
          jurisdiction?: string | null
          lease_id: string
          legal_id?: string
          provision_description?: string | null
          provision_type?: string | null
          requirements?: string | null
        }
        Update: {
          jurisdiction?: string | null
          lease_id?: string
          legal_id?: string
          provision_description?: string | null
          provision_type?: string | null
          requirements?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "legal_general_provisions_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      maintenance_obligations: {
        Row: {
          cost_responsibility: string | null
          lease_id: string
          maintenance_description: string | null
          maintenance_id: string
          maintenance_type: string | null
          notification_requirements: string | null
          responsible_party: string | null
        }
        Insert: {
          cost_responsibility?: string | null
          lease_id: string
          maintenance_description?: string | null
          maintenance_id?: string
          maintenance_type?: string | null
          notification_requirements?: string | null
          responsible_party?: string | null
        }
        Update: {
          cost_responsibility?: string | null
          lease_id?: string
          maintenance_description?: string | null
          maintenance_id?: string
          maintenance_type?: string | null
          notification_requirements?: string | null
          responsible_party?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "maintenance_obligations_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      messages: {
        Row: {
          chat_session_id: string
          content: string
          created_at: string
          id: string
          lease_id: string | null
          metadata: Json | null
          sender: string
          user_id: string | null
        }
        Insert: {
          chat_session_id: string
          content: string
          created_at?: string
          id?: string
          lease_id?: string | null
          metadata?: Json | null
          sender: string
          user_id?: string | null
        }
        Update: {
          chat_session_id?: string
          content?: string
          created_at?: string
          id?: string
          lease_id?: string | null
          metadata?: Json | null
          sender?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_chat_session_id_fkey"
            columns: ["chat_session_id"]
            isOneToOne: false
            referencedRelation: "chat_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
          {
            foreignKeyName: "messages_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      notice_provisions: {
        Row: {
          delivery_methods: string | null
          lease_id: string
          notice_address: string | null
          notice_id: string
          party_type: string | null
          service_domicile: string | null
        }
        Insert: {
          delivery_methods?: string | null
          lease_id: string
          notice_address?: string | null
          notice_id?: string
          party_type?: string | null
          service_domicile?: string | null
        }
        Update: {
          delivery_methods?: string | null
          lease_id?: string
          notice_address?: string | null
          notice_id?: string
          party_type?: string | null
          service_domicile?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notice_provisions_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      parties: {
        Row: {
          address_city: string | null
          address_postal_code: string | null
          address_province: string | null
          address_street: string | null
          authorization_details: string | null
          email: string | null
          lease_id: string
          legal_status: string | null
          party_id: string
          party_name: string | null
          party_type: string | null
          phone_number: string | null
          representative_name: string | null
          representative_title: string | null
        }
        Insert: {
          address_city?: string | null
          address_postal_code?: string | null
          address_province?: string | null
          address_street?: string | null
          authorization_details?: string | null
          email?: string | null
          lease_id: string
          legal_status?: string | null
          party_id?: string
          party_name?: string | null
          party_type?: string | null
          phone_number?: string | null
          representative_name?: string | null
          representative_title?: string | null
        }
        Update: {
          address_city?: string | null
          address_postal_code?: string | null
          address_province?: string | null
          address_street?: string | null
          authorization_details?: string | null
          email?: string | null
          lease_id?: string
          legal_status?: string | null
          party_id?: string
          party_name?: string | null
          party_type?: string | null
          phone_number?: string | null
          representative_name?: string | null
          representative_title?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "parties_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      profiles: {
        Row: {
          created_at: string | null
          email: string | null
          id: string
          name: string | null
          role: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email?: string | null
          id: string
          name?: string | null
          role?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string | null
          id?: string
          name?: string | null
          role?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      properties: {
        Row: {
          building_address: string | null
          cadastre_details: string | null
          land_lot_number: string | null
          lease_id: string
          leased_premises_address: string | null
          measurement_method: string | null
          plan_reference: string | null
          property_condition: string | null
          property_id: string
          rental_area_sqft: number | null
        }
        Insert: {
          building_address?: string | null
          cadastre_details?: string | null
          land_lot_number?: string | null
          lease_id: string
          leased_premises_address?: string | null
          measurement_method?: string | null
          plan_reference?: string | null
          property_condition?: string | null
          property_id?: string
          rental_area_sqft?: number | null
        }
        Update: {
          building_address?: string | null
          cadastre_details?: string | null
          land_lot_number?: string | null
          lease_id?: string
          leased_premises_address?: string | null
          measurement_method?: string | null
          plan_reference?: string | null
          property_condition?: string | null
          property_id?: string
          rental_area_sqft?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "properties_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      security_deposits: {
        Row: {
          deposit_id: string
          lease_id: string
          security_deposit_base: number | null
          security_deposit_payment_method: string | null
          security_deposit_taxes: number | null
          security_deposit_total: number | null
        }
        Insert: {
          deposit_id?: string
          lease_id: string
          security_deposit_base?: number | null
          security_deposit_payment_method?: string | null
          security_deposit_taxes?: number | null
          security_deposit_total?: number | null
        }
        Update: {
          deposit_id?: string
          lease_id?: string
          security_deposit_base?: number | null
          security_deposit_payment_method?: string | null
          security_deposit_taxes?: number | null
          security_deposit_total?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "security_deposits_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      signage_provisions: {
        Row: {
          approval_required: boolean | null
          cost_responsibility: string | null
          lease_id: string
          removal_requirements: string | null
          signage_id: string
          signage_restrictions: string | null
          signage_type: string | null
        }
        Insert: {
          approval_required?: boolean | null
          cost_responsibility?: string | null
          lease_id: string
          removal_requirements?: string | null
          signage_id?: string
          signage_restrictions?: string | null
          signage_type?: string | null
        }
        Update: {
          approval_required?: boolean | null
          cost_responsibility?: string | null
          lease_id?: string
          removal_requirements?: string | null
          signage_id?: string
          signage_restrictions?: string | null
          signage_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "signage_provisions_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      signatures: {
        Row: {
          execution_date: string | null
          lease_id: string
          party_type: string | null
          signatory_name: string | null
          signatory_title: string | null
          signature_id: string
        }
        Insert: {
          execution_date?: string | null
          lease_id: string
          party_type?: string | null
          signatory_name?: string | null
          signatory_title?: string | null
          signature_id?: string
        }
        Update: {
          execution_date?: string | null
          lease_id?: string
          party_type?: string | null
          signatory_name?: string | null
          signatory_title?: string | null
          signature_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "signatures_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      use_restrictions: {
        Row: {
          lease_id: string
          restriction_category: string | null
          restriction_description: string | null
          restriction_id: string
          restriction_type: string | null
        }
        Insert: {
          lease_id: string
          restriction_category?: string | null
          restriction_description?: string | null
          restriction_id?: string
          restriction_type?: string | null
        }
        Update: {
          lease_id?: string
          restriction_category?: string | null
          restriction_description?: string | null
          restriction_id?: string
          restriction_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "use_restrictions_lease_id_fkey"
            columns: ["lease_id"]
            isOneToOne: false
            referencedRelation: "lease_documents"
            referencedColumns: ["lease_id"]
          },
        ]
      }
      user_property_access: {
        Row: {
          granted_at: string | null
          granted_by: string | null
          id: string
          lease_id: string
          user_id: string
        }
        Insert: {
          granted_at?: string | null
          granted_by?: string | null
          id?: string
          lease_id: string
          user_id: string
        }
        Update: {
          granted_at?: string | null
          granted_by?: string | null
          id?: string
          lease_id?: string
          user_id?: string
        }
        Relationships: []
      }
      user_roles: {
        Row: {
          assigned_at: string | null
          assigned_by: string | null
          id: string
          role: Database["public"]["Enums"]["user_role"]
          user_id: string
        }
        Insert: {
          assigned_at?: string | null
          assigned_by?: string | null
          id?: string
          role: Database["public"]["Enums"]["user_role"]
          user_id: string
        }
        Update: {
          assigned_at?: string | null
          assigned_by?: string | null
          id?: string
          role?: Database["public"]["Enums"]["user_role"]
          user_id?: string
        }
        Relationships: []
      }
      users: {
        Row: {
          created_at: string | null
          email: string | null
          id: string
          name: string | null
          role: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email?: string | null
          id?: string
          name?: string | null
          role?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string | null
          id?: string
          name?: string | null
          role?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      current_view: {
        Row: {
          id: string | null;
          lease_id: string | null;
          pad_form_sent_date: string | null;
          pad_form_entered_date: string | null;
          tenant_trade_name: string | null;
          tenant_preferred_language: string | null;
          prepared_by: string | null;
          landlord_work_description: string | null;
          installation_period_description: string | null;
          utilities_during_fixturing: string | null;
          opex_during_fixturing: string | null;
          commencement_conditions: string | null;
          movable_hypothec: string | null;
          lease_type: string | null;
          percentage_rent_applicable: boolean | null;
          floor_suite_number: string | null;
          usable_area_sqft: string | null;
          security_deposit_terms_and_conditions: string | null;
          proportionate_share_operating_expenses: string | null;
          proportionate_share_taxes: string | null;
          tenant_allowance_description: string | null;
          free_rent_payment_terms: string | null;
          created_at: string | null;
          updated_at: string | null;
        }
        Relationships: []
      }
      user_management_view: {
        Row: {
          assigned_at: string | null
          assigned_by: string | null
          created_at: string | null
          email: string | null
          id: string | null
          name: string | null
          role: Database["public"]["Enums"]["user_role"] | null
        }
        Relationships: []
      }
    }
    Functions: {
      get_current_user_role: {
        Args: Record<PropertyKey, never>
        Returns: Database["public"]["Enums"]["user_role"]
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      update_document_complete_content: {
        Args: { doc_id: string }
        Returns: undefined
      }
      user_has_lease_access: {
        Args: { lease_uuid: string }
        Returns: boolean
      }
    }
    Enums: {
      document_language: "English" | "Français"
      user_role:
        | "administrator"
        | "property_manager"
        | "maintenance_coordinator"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      document_language: ["English", "Français"],
      user_role: [
        "administrator",
        "property_manager",
        "maintenance_coordinator",
      ],
    },
  },
} as const
