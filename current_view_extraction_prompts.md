# Current View Table - LLM Extraction Prompts

## System Prompt
```
You are a specialized AI assistant for extracting specific information from commercial lease documents. Your task is to identify and extract precise data to populate the current_view table fields that are missing from the existing lease database structure. Focus on finding factual information explicitly stated in the document. When information is ambiguous or missing, return null rather than making assumptions. Always return results in valid JSON format.
```

---

## Current View Table Field Extraction Prompts

### 1. pad_form_sent_date (Date)
**Description**: Date when PAD (Pre-Authorized Debit) form was sent to tenant.
**Prompt**:
```
Find when a PAD form or automatic payment authorization was sent to the tenant. Look for dates mentioned with "PAD form sent" or payment setup correspondence. Return date in YYYY-MM-DD format.
```

### 2. pad_form_entered_date (Date)
**Description**: Date when PAD form was entered into property management system.
**Prompt**:
```
Find when PAD information was entered into the system. Look for "entered into Hopem," "processed in system," or payment method activation dates. Return date in YYYY-MM-DD format.
```

### 3. tenant_trade_name (Text)
**Description**: Business/trade name tenant operates under (different from legal name).
**Prompt**:
```
Find the tenant's trade name or business name (DBA). Look for "trade name," "business name," "doing business as," or store/brand names different from the legal entity name.
```

### 4. tenant_preferred_language (Text)
**Description**: Tenant's preferred language for communications.
**Prompt**:
```
Find the tenant's preferred communication language. Look for "preferred language," "correspondence language," or document language requirements. Return language name (e.g., "English", "French").
```

### 5. prepared_by (Text)
**Description**: Leasing representative who prepared/negotiated the lease.
**Prompt**: 
```
Find the leasing representative who prepared this lease. Look for "Leasing Rep:", "Prepared by:", "Leasing Agent:", or broker names. Extract the person's full name.
```

### 6. landlord_work_description (Text)
**Description**: Description of work to be performed by landlord.
**Prompt**:
```
Find details about landlord work. Look for "LANDLORD WORK:", "LL Work:", tenant improvements, or work to be completed by landlord before or during tenancy.
```

### 7. installation_period_description (Text)
**Description**: Details about the installation/fixturing period.
**Prompt**:
```
Find installation or fixturing period details. Look for "FIXTURING PERIOD:", "installation period," build-out time, or tenant setup period descriptions.
```

### 8. utilities_during_fixturing (Text)
**Description**: Utility arrangements during fixturing period.
**Prompt**:
```
Find utility arrangements during fixturing. Look for "UTILITIES DURING FIXTURING:", who pays utilities during build-out, or utility responsibility during installation period.
```

### 9. opex_during_fixturing (Text)
**Description**: Operating expense arrangements during fixturing period.
**Prompt**:
```
Find OPEX/CAM arrangements during fixturing. Look for "OPEX/CAM DURING FIXTURING:", operating cost responsibility during build-out period.
```

### 10. commencement_conditions (Text)
**Description**: Conditions that must be met for lease commencement.
**Prompt**:
```
Find lease commencement conditions. Look for "COMMENCEMENT DATE" conditions, requirements for lease start, delivery conditions, or prerequisites for occupancy.
```

### 11. movable_hypothec (Text)
**Description**: Movable hypothec provisions if applicable.
**Prompt**:
```
Find movable hypothec information. Look for "MOVABLE HYPOTHEC:", security interests in tenant's movable property, or chattel mortgage provisions.
```

### 12. lease_type (Text)
**Description**: Type of lease (Net, Gross, Modified Gross, etc.).
**Prompt**:
```
Find the lease type. Look for "TYPE OF LEASE:", "Net," "Gross," "Modified Gross," "Triple Net," or similar lease structure descriptions.
```

### 13. percentage_rent_applicable (Boolean)
**Description**: Whether percentage rent applies to this lease.
**Prompt**:
```
Determine if percentage rent applies. Look for "percentage rent," "% rent," "sales-based rent," or revenue-sharing provisions. Return true if mentioned, false otherwise.
```

### 14. floor_suite_number (Text)
**Description**: Floor and suite number designation.
**Prompt**:
```
Find floor and suite number. Look for "FLOOR/SUITE," suite numbers, floor designations, or unit identifiers in premises description.
```

### 15. usable_area_sqft (Number)
**Description**: Usable area in square feet.
**Prompt**:
```
Find usable area measurement. Look for "USABLE AREA," "usable square feet," or area measurements excluding common areas. Return numeric value only.
```

### 16. security_deposit_terms_and_conditions (Text)
**Description**: Terms and conditions for security deposit.
**Prompt**:
```
Find security deposit terms. Look for "SECURITY DEPOSIT" conditions, return requirements, usage restrictions, or deposit handling terms.
```

### 17. proportionate_share_operating_expenses (Number)
**Description**: Tenant's proportionate share percentage for operating expenses.
**Prompt**:
```
Find proportionate share for operating expenses. Look for "Operating Expenses:" percentage, "OPEX" share, or tenant's portion of building operating costs. Return numeric value only (without % symbol).
```

### 18. proportionate_share_taxes (Number)
**Description**: Tenant's proportionate share percentage for real estate taxes.
**Prompt**:
```
Find proportionate share for taxes. Look for "Real Estate Taxes:" percentage, tax share, or tenant's portion of property taxes. Return numeric value only (without % symbol).
```

### 19. tenant_allowance_description (Text)
**Description**: Description of tenant improvement allowance terms.
**Prompt**:
```
Find tenant allowance terms. Look for "TENANT ALLOWANCE (TI)" description, "TI" conditions, improvement allowance usage requirements, or construction allowance terms.
```

### 20. free_rent_payment_terms (Text)
**Description**: Terms and conditions for free rent periods.
**Prompt**:
```
Find free rent terms. Look for "FREE RENT" conditions, rent-free period descriptions, or abatement terms and conditions.
```

---

## Usage Instructions

### Output Format
Request results in JSON format for all current_view table fields:
```json
{
  "pad_form_sent_date": "2025-04-15",
  "pad_form_entered_date": "2025-04-16",
  "tenant_trade_name": "Gestion MarCan Inc.",
  "tenant_preferred_language": "English",
  "prepared_by": "Jordan Altman",
  "landlord_work_description": "As-Is",
  "installation_period_description": "3 months & 29 days",
  "utilities_during_fixturing": "T",
  "opex_during_fixturing": "LL",
  "commencement_conditions": "on or around June 1, 2025",
  "movable_hypothec": "N/A",
  "lease_type": "Net",
  "percentage_rent_applicable": false,
  "floor_suite_number": "350 & 375",
  "usable_area_sqft": 2611,
  "security_deposit_terms_and_conditions": "Balance to be held by LL for any of T's defaults. To be remitted 60 days following expiry of L",
  "proportionate_share_operating_expenses": 15.2,
  "proportionate_share_taxes": 15.2,
  "tenant_allowance_description": "$52,220.00 plus applicable taxes & $20.00/SF",
  "free_rent_payment_terms": "Net"
}
```

### Error Handling
- Return `null` for fields where information cannot be found
- Ensure dates are in YYYY-MM-DD format and logical
- Return numeric values without currency symbols or formatting
- Return boolean true/false for percentage_rent_applicable
- Format names and text in appropriate case
- These fields supplement existing lease data structure
